import request from '@/config/axios'

// 会员地址 VO
export interface MemberAddressVO {
  id: number // 主键ID
  memberId: number // 会员ID（逻辑关联 rental_member.id）
  contactName: string // 联系人姓名
  contactPhone: string // 联系人手机号
  province: string // 省
  city: string // 市
  district: string // 区/县
  detailAddress: string // 详细地址
  isDefault: boolean // 是否为默认地址（1-是，0-否）
}

// 会员地址 API
export const MemberAddressApi = {
  // 查询会员地址分页
  getMemberAddressPage: async (params: any) => {
    return await request.get({ url: `/rental/member-address/page`, params })
  },

  // 查询会员地址详情
  getMemberAddress: async (id: number) => {
    return await request.get({ url: `/rental/member-address/get?id=` + id })
  },

  // 新增会员地址
  createMemberAddress: async (data: MemberAddressVO) => {
    return await request.post({ url: `/rental/member-address/create`, data })
  },

  // 修改会员地址
  updateMemberAddress: async (data: MemberAddressVO) => {
    return await request.put({ url: `/rental/member-address/update`, data })
  },

  // 删除会员地址
  deleteMemberAddress: async (id: number) => {
    return await request.delete({ url: `/rental/member-address/delete?id=` + id })
  },

  // 导出会员地址 Excel
  exportMemberAddress: async (params) => {
    return await request.download({ url: `/rental/member-address/export-excel`, params })
  },
}