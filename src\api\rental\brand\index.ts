import request from '@/config/axios'

// 车辆品牌 VO
export interface BrandVO {
  id: number // 主键ID
  name: string // 品牌名称
  description: string // 品牌描述
  enabled: boolean // 是否启用（1=启用，0=禁用）
  sort: number // 排序值（越小越靠前）
}

// 车辆品牌 API
export const BrandApi = {
  // 查询车辆品牌分页
  getBrandPage: async (params: any) => {
    return await request.get({ url: `/rental/brand/page`, params })
  },

  // 查询车辆品牌详情
  getBrand: async (id: number) => {
    return await request.get({ url: `/rental/brand/get?id=` + id })
  },

  // 新增车辆品牌
  createBrand: async (data: BrandVO) => {
    return await request.post({ url: `/rental/brand/create`, data })
  },

  // 修改车辆品牌
  updateBrand: async (data: BrandVO) => {
    return await request.put({ url: `/rental/brand/update`, data })
  },

  // 删除车辆品牌
  deleteBrand: async (id: number) => {
    return await request.delete({ url: `/rental/brand/delete?id=` + id })
  },

  // 导出车辆品牌 Excel
  exportBrand: async (params) => {
    return await request.download({ url: `/rental/brand/export-excel`, params })
  },
}