import request from '@/config/axios'

// 实名认证 VO
export interface MemberIdentityVO {
  id: number // 主键ID
  memberId: number // 会员ID（逻辑关联 rental_member.id）
  realName: string // 真实姓名
  idCardNumber: string // 身份证号码
  idCardFrontUrl: string // 身份证正面照片URL
  idCardBackUrl: string // 身份证反面照片URL
  status: number // 认证状态（0-待审核，1-已认证，2-未通过）
  verifiedAt: string // 认证通过时间
}

// 实名认证 API
export const MemberIdentityApi = {
  // 删除实名认证
  deleteMemberIdentity: async (id: number) => {
    return await request.delete({ url: `/rental/member-identity/delete?id=` + id })
  },

  // 根据会员ID查询实名认证信息
  getMemberIdentityByMemberId: async (memberId: number) => {
    return await request.get({ url: `/rental/member-identity/get-by-member-id?memberId=` + memberId })
  },
}