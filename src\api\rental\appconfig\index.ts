import request from '@/config/axios'

// APP端配置 VO
export interface AppConfigVO {
  id: number // 主键ID
  name: string // 配置名称
  type: string // 配置类型
  code: string // 配置编码（唯一标识）
  content: string // 富文本内容，支持 HTML 或 Markdown
}

// APP端配置 API
export const AppConfigApi = {
  // 查询APP端配置分页
  getAppConfigPage: async (params: any) => {
    return await request.get({ url: `/rental/app-config/page`, params })
  },

  // 查询APP端配置详情
  getAppConfig: async (id: number) => {
    return await request.get({ url: `/rental/app-config/get?id=` + id })
  },

  // 新增APP端配置
  createAppConfig: async (data: AppConfigVO) => {
    return await request.post({ url: `/rental/app-config/create`, data })
  },

  // 修改APP端配置
  updateAppConfig: async (data: AppConfigVO) => {
    return await request.put({ url: `/rental/app-config/update`, data })
  },

  // 删除APP端配置
  deleteAppConfig: async (id: number) => {
    return await request.delete({ url: `/rental/app-config/delete?id=` + id })
  },

  // 导出APP端配置 Excel
  exportAppConfig: async (params) => {
    return await request.download({ url: `/rental/app-config/export-excel`, params })
  },
}