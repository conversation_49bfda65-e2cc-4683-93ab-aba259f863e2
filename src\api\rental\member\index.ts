import request from '@/config/axios'

// 会员 VO
export interface MemberVO {
  id: number // 主键ID
  openid: string // OpenID
  nickname: string // 用户昵称
  avatarUrl: string // 用户头像
  phoneNumber: string // 手机号
  gender: number // 性别
  status: boolean // 状态（1-关闭，0-开启）
  createTime: string // 创建时间
}

// 会员 API
export const MemberApi = {
  // 查询会员分页
  getMemberPage: async (params: any) => {
    return await request.get({ url: `/rental/member/page`, params })
  },

  // 查询会员详情
  getMember: async (id: number) => {
    return await request.get({ url: `/rental/member/get?id=` + id })
  },

  // 新增会员
  createMember: async (data: MemberVO) => {
    return await request.post({ url: `/rental/member/create`, data })
  },

  // 修改会员
  updateMember: async (data: MemberVO) => {
    return await request.put({ url: `/rental/member/update`, data })
  },

  // 删除会员
  deleteMember: async (id: number) => {
    return await request.delete({ url: `/rental/member/delete?id=` + id })
  },

  // 导出会员 Excel
  exportMember: async (params) => {
    return await request.download({ url: `/rental/member/export-excel`, params })
  },
}