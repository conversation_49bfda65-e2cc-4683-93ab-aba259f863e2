import request from '@/config/axios'

// 轮播图 VO
export interface HomeBannerVO {
  id: number // 主键ID
  title: string // 标题
  imageUrl: string // 图片地址
  linkType: string // 跳转类型（none=不跳转，url=外链，page=小程序页面）
  linkValue: string // 跳转内容
  sort: number // 排序
  enabled: boolean // 是否启用（1=启用，0=禁用）
  remark: string // 备注
}

// 轮播图 API
export const HomeBannerApi = {
  // 查询轮播图分页
  getHomeBannerPage: async (params: any) => {
    return await request.get({ url: `/rental/home-banner/page`, params })
  },

  // 查询轮播图详情
  getHomeBanner: async (id: number) => {
    return await request.get({ url: `/rental/home-banner/get?id=` + id })
  },

  // 新增轮播图
  createHomeBanner: async (data: HomeBannerVO) => {
    return await request.post({ url: `/rental/home-banner/create`, data })
  },

  // 修改轮播图
  updateHomeBanner: async (data: HomeBannerVO) => {
    return await request.put({ url: `/rental/home-banner/update`, data })
  },

  // 删除轮播图
  deleteHomeBanner: async (id: number) => {
    return await request.delete({ url: `/rental/home-banner/delete?id=` + id })
  },

  // 导出轮播图 Excel
  exportHomeBanner: async (params) => {
    return await request.download({ url: `/rental/home-banner/export-excel`, params })
  },
}